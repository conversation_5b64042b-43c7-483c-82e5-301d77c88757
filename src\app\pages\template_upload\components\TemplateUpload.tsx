"use client";

import { useState } from "react";
import { Upload, FileText, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import mammoth from "mammoth";
import { DocumentContent } from "../page";

interface TemplateUploadProps {
  onDocumentUploaded: (content: DocumentContent) => void;
}

export function TemplateUpload({ onDocumentUploaded }: TemplateUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const processDocument = async (file: File) => {
    setIsProcessing(true);
    
    try {
      // Convert file to ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      
      // Use mammoth to extract HTML and text from DOCX
      const result = await mammoth.convertToHtml({ arrayBuffer });
      
      if (result.messages.length > 0) {
        console.warn("Mammoth conversion warnings:", result.messages);
      }

      const documentContent: DocumentContent = {
        html: result.value,
        text: await mammoth.extractRawText({ arrayBuffer }).then(r => r.value),
        originalFile: file,
        fileName: file.name
      };

      toast.success("Document analyzed successfully!");
      onDocumentUploaded(documentContent);
      
    } catch (error) {
      console.error("Error processing document:", error);
      toast.error("Failed to process document. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileUpload = (file: File) => {
    // Check if file is DOCX
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];
    
    if (!validTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.docx')) {
      toast.error("Please upload a valid DOCX file");
      return;
    }

    setUploadedFile(file);
    processDocument(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) handleFileUpload(file);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) handleFileUpload(file);
  };

  return (
    <div className="flex items-center justify-center min-h-screen px-4 py-8">
      <div className="w-full max-w-2xl">
        <div className="mb-8 text-center space-y-2">
          <h1 className="text-3xl font-semibold text-foreground">Upload Document Template</h1>
          <p className="text-muted-foreground text-lg">
            Upload a DOCX file to analyze and edit its content
          </p>
        </div>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-card-foreground flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Document Upload
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`
                relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300
                ${
                  isDragOver
                    ? "border-primary bg-primary/10"
                    : "border-border hover:border-primary/50"
                }
                ${isProcessing ? "opacity-50 pointer-events-none" : ""}
              `}
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOver(true);
              }}
              onDragLeave={() => setIsDragOver(false)}
            >
              {isProcessing ? (
                <div className="space-y-4">
                  <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                  <div>
                    <p className="text-foreground mb-2">Processing document...</p>
                    <p className="text-muted-foreground text-sm">
                      Analyzing content and structure
                    </p>
                  </div>
                </div>
              ) : uploadedFile ? (
                <div className="space-y-4">
                  <FileText className="w-16 h-16 text-primary mx-auto" />
                  <div>
                    <p className="text-foreground mb-2 font-medium">{uploadedFile.name}</p>
                    <p className="text-muted-foreground text-sm">
                      File uploaded successfully
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById("file-input")?.click()}
                  >
                    Choose Different File
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <Upload className="w-16 h-16 text-muted-foreground mx-auto" />
                  <div>
                    <p className="text-foreground mb-2 text-lg font-medium">
                      Click to upload or drag and drop
                    </p>
                    <p className="text-muted-foreground">
                      DOCX files only (Microsoft Word documents)
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById("file-input")?.click()}
                  >
                    Browse Files
                  </Button>
                </div>
              )}
              
              <input
                id="file-input"
                type="file"
                accept=".docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                onChange={handleFileSelect}
                className="hidden"
                disabled={isProcessing}
              />
            </div>

            <div className="mt-6 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                <div className="text-sm text-muted-foreground">
                  <p className="font-medium mb-1">Supported features:</p>
                  <ul className="space-y-1 list-disc list-inside ml-2">
                    <li>Text content extraction and editing</li>
                    <li>Basic formatting (bold, italic, underline)</li>
                    <li>Paragraph and heading styles</li>
                    <li>Document structure preservation</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
