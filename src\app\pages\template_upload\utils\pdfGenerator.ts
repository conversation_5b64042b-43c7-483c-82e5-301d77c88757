import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface PDFOptions {
  fileName: string;
  orientation?: 'portrait' | 'landscape';
}

export async function generatePDF(element: HTMLElement, options: PDFOptions): Promise<void> {
  try {
    // A4 dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI
    const A4_LANDSCAPE_WIDTH = 1123;  // 11.69 inches * 96 DPI
    const A4_LANDSCAPE_HEIGHT = 794;  // 8.27 inches * 96 DPI

    const isLandscape = options.orientation === 'landscape';
    const canvasWidth = isLandscape ? A4_LANDSCAPE_WIDTH : A4_PORTRAIT_WIDTH;
    const canvasHeight = isLandscape ? A4_LANDSCAPE_HEIGHT : A4_PORTRAIT_HEIGHT;

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px'; // Move off-screen
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${canvasWidth}px`;
    clonedElement.style.height = `${canvasHeight}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(clonedElement, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: canvasWidth,
      height: canvasHeight,
      scrollX: 0,
      scrollY: 0,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    // Create PDF with specified orientation
    const pdf = new jsPDF({
      orientation: isLandscape ? 'landscape' : 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Convert canvas to image and add to PDF
    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

    // Generate filename
    const filename = options.fileName || 'document.pdf';

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF');
  }
}

// Alternative method using File constructor for better browser compatibility
export async function generatePDFWithFileConstructor(element: HTMLElement, options: PDFOptions): Promise<void> {
  try {
    // A4 dimensions in pixels (at 96 DPI)
    const A4_PORTRAIT_WIDTH = 794;   // 8.27 inches * 96 DPI
    const A4_PORTRAIT_HEIGHT = 1123; // 11.69 inches * 96 DPI
    const A4_LANDSCAPE_WIDTH = 1123;  // 11.69 inches * 96 DPI
    const A4_LANDSCAPE_HEIGHT = 794;  // 8.27 inches * 96 DPI

    const isLandscape = options.orientation === 'landscape';
    const canvasWidth = isLandscape ? A4_LANDSCAPE_WIDTH : A4_PORTRAIT_WIDTH;
    const canvasHeight = isLandscape ? A4_LANDSCAPE_HEIGHT : A4_PORTRAIT_HEIGHT;

    // Create a clone of the element to avoid visual disruption
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Set up the clone with proper styling for capture
    clonedElement.style.transform = 'scale(1)';
    clonedElement.style.transformOrigin = 'top left';
    clonedElement.style.position = 'absolute';
    clonedElement.style.top = '-9999px'; // Move off-screen
    clonedElement.style.left = '-9999px';
    clonedElement.style.width = `${canvasWidth}px`;
    clonedElement.style.height = `${canvasHeight}px`;
    clonedElement.style.backgroundColor = '#ffffff';

    // Add clone to document temporarily
    document.body.appendChild(clonedElement);

    const canvas = await html2canvas(clonedElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: canvasWidth,
      height: canvasHeight,
    });

    // Remove the clone from document
    document.body.removeChild(clonedElement);

    const pdf = new jsPDF({
      orientation: isLandscape ? 'landscape' : 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Get A4 portrait dimensions in mm
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

    // Get PDF as blob
    const pdfBlob = pdf.output('blob');
    
    // Create filename
    const filename = options.fileName || 'document.pdf';

    // Create file and trigger download
    const file = new File([pdfBlob], filename, { type: 'application/pdf' });
    const url = URL.createObjectURL(file);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    // Clean up
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF with File constructor:', error);
    throw new Error('Failed to generate PDF');
  }
}
