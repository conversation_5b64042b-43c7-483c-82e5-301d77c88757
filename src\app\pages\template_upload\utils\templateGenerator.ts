import { LayoutSettings } from "../page";

export interface TemplateData {
  name: string;
  displayName: string;
  content: string;
  placeholders: string[];
  layoutSettings: LayoutSettings;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'date' | 'select';
  required: boolean;
  options?: string[];
}

export function generateTemplateFiles(templateData: TemplateData) {
  const { name, displayName, content, placeholders, layoutSettings } = templateData;
  
  // Generate form fields from placeholders
  const formFields = generateFormFields(placeholders);
  
  // Generate the main page component
  const pageComponent = generatePageComponent(name, displayName, content, formFields);
  
  // Generate the form component
  const formComponent = generateFormComponent(name, displayName, formFields);
  
  // Generate the preview component
  const previewComponent = generatePreviewComponent(name, displayName, content, layoutSettings, formFields);
  
  return {
    pageComponent,
    formComponent,
    previewComponent,
    formFields
  };
}

function generateFormFields(placeholders: string[]): FormField[] {
  return placeholders.map(placeholder => {
    const fieldName = placeholder.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    // Determine field type based on placeholder name
    let type: FormField['type'] = 'text';
    if (placeholder.toLowerCase().includes('date')) {
      type = 'date';
    } else if (placeholder.toLowerCase().includes('address') || 
               placeholder.toLowerCase().includes('description') ||
               placeholder.toLowerCase().includes('reason')) {
      type = 'textarea';
    }
    
    return {
      name: fieldName,
      label: placeholder,
      type,
      required: true
    };
  });
}

function generatePageComponent(
  name: string,
  displayName: string,
  content: string,
  formFields: FormField[]
): string {
  return `"use client";

import { useState } from "react";
import { ${capitalizeFirst(name)}Form } from "./components/${capitalizeFirst(name)}Form";
import { ${capitalizeFirst(name)}Preview } from "./components/${capitalizeFirst(name)}Preview";

export interface ${capitalizeFirst(name)}Data {
${formFields.map(field => `  ${field.name}: string;`).join('\n')}
}

export default function ${capitalizeFirst(name)}Page() {
  const [formData, setFormData] = useState<${capitalizeFirst(name)}Data>({
${formFields.map(field => `    ${field.name}: "",`).join('\n')}
  });
  const [currentStep, setCurrentStep] = useState<"form" | "preview">("form");

  const handleFormSubmit = (data: ${capitalizeFirst(name)}Data) => {
    setFormData(data);
    setCurrentStep("preview");
  };

  const handleBackToForm = () => {
    setCurrentStep("form");
  };

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      {currentStep === "form" && (
        <${capitalizeFirst(name)}Form
          initialData={formData}
          onSubmit={handleFormSubmit}
        />
      )}
      
      {currentStep === "preview" && (
        <${capitalizeFirst(name)}Preview
          data={formData}
          onBack={handleBackToForm}
          onEdit={handleBackToForm}
        />
      )}
    </div>
  );
}`;
}

function generateFormComponent(name: string, displayName: string, formFields: FormField[]): string {
  return `"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { ${capitalizeFirst(name)}Data } from "../page";

interface ${capitalizeFirst(name)}FormProps {
  initialData: ${capitalizeFirst(name)}Data;
  onSubmit: (data: ${capitalizeFirst(name)}Data) => void;
}

export function ${capitalizeFirst(name)}Form({ initialData, onSubmit }: ${capitalizeFirst(name)}FormProps) {
  const [formData, setFormData] = useState<${capitalizeFirst(name)}Data>(initialData);

  const handleInputChange = (field: keyof ${capitalizeFirst(name)}Data, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    const requiredFields = [${formFields.filter(f => f.required).map(f => `"${f.name}"`).join(', ')}];
    const emptyFields = requiredFields.filter(field => !formData[field as keyof ${capitalizeFirst(name)}Data]);
    
    if (emptyFields.length > 0) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    onSubmit(formData);
  };

  return (
    <div className="flex items-center justify-center min-h-screen px-4 py-8">
      <div className="w-full max-w-2xl">
        <div className="mb-8 text-center space-y-2">
          <h1 className="text-3xl font-semibold text-foreground">${displayName}</h1>
          <p className="text-muted-foreground text-lg">
            Fill in the details to generate your document
          </p>
        </div>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-card-foreground">Document Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
${formFields.map(field => generateFormFieldJSX(field)).join('\n\n')}
              
              <Button type="submit" className="w-full">
                Generate Document
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}`;
}

function generateFormFieldJSX(field: FormField): string {
  const commonProps = `
                value={formData.${field.name}}
                onChange={(e) => handleInputChange("${field.name}", e.target.value)}
                required={${field.required}}`;

  switch (field.type) {
    case 'textarea':
      return `              <div className="space-y-2">
                <Label htmlFor="${field.name}">${field.label}${field.required ? ' *' : ''}</Label>
                <Textarea
                  id="${field.name}"
                  placeholder="Enter ${field.label.toLowerCase()}"${commonProps}
                />
              </div>`;
    
    case 'date':
      return `              <div className="space-y-2">
                <Label htmlFor="${field.name}">${field.label}${field.required ? ' *' : ''}</Label>
                <Input
                  id="${field.name}"
                  type="date"${commonProps}
                />
              </div>`;
    
    default:
      return `              <div className="space-y-2">
                <Label htmlFor="${field.name}">${field.label}${field.required ? ' *' : ''}</Label>
                <Input
                  id="${field.name}"
                  type="text"
                  placeholder="Enter ${field.label.toLowerCase()}"${commonProps}
                />
              </div>`;
  }
}

function generatePreviewComponent(name: string, displayName: string, content: string, layoutSettings: LayoutSettings, formFields: FormField[]): string {
  const processTemplateFunction = `function processTemplate(template: string, data: ${capitalizeFirst(name)}Data): string {
  let processed = template;

  // Replace placeholders with actual data
${formFields.map(field => `  processed = processed.replace(/\\{${field.label}\\}/g, data.${field.name} || "{${field.label}}");`).join('\n')}

  return processed;
}`;

  return `"use client";

import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Edit, FileText } from "lucide-react";
import { toast } from "sonner";
import { ${capitalizeFirst(name)}Data } from "../page";
import { generatePDF } from "../../template_upload/utils/pdfGenerator";

interface ${capitalizeFirst(name)}PreviewProps {
  data: ${capitalizeFirst(name)}Data;
  onBack: () => void;
  onEdit: () => void;
}

export function ${capitalizeFirst(name)}Preview({ data, onBack, onEdit }: ${capitalizeFirst(name)}PreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(0.8);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const handleDownloadPDF = async () => {
    if (!previewRef.current) return;

    setIsGeneratingPDF(true);
    try {
      await generatePDF(previewRef.current, {
        fileName: "${displayName.replace(/[^a-zA-Z0-9]/g, '_')}.pdf",
        orientation: "${layoutSettings.orientation}",
        paperSize: "${layoutSettings.paperSize}",
      });
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const processedContent = processTemplate(\`${content.replace(/`/g, '\\`')}\`, data);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Form
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">${displayName} Preview</h1>
            <p className="text-muted-foreground">Review your document before downloading</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Details
          </Button>
          <Button
            onClick={handleDownloadPDF}
            disabled={isGeneratingPDF}
          >
            <Download className="w-4 h-4 mr-2" />
            {isGeneratingPDF ? "Generating..." : "Download PDF"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Controls */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Preview Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Zoom Level</h4>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(0.6)}
                >
                  60%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(0.8)}
                >
                  80%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(1.0)}
                >
                  100%
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-lg">Document Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-8 rounded-lg overflow-auto max-h-[800px]">
              <div
                ref={previewRef}
                className="bg-white shadow-lg mx-auto"
                style={{
                  transform: \`scale(\${scale})\`,
                  transformOrigin: "top center",
                  width: "${layoutSettings.orientation === 'portrait' ? (layoutSettings.paperSize === 'a4' ? '8.27in' : '8.5in') : (layoutSettings.paperSize === 'a4' ? '11.69in' : '11in')}",
                  minHeight: "${layoutSettings.orientation === 'portrait' ? (layoutSettings.paperSize === 'a4' ? '11.69in' : '11in') : (layoutSettings.paperSize === 'a4' ? '8.27in' : '8.5in')}",
                  padding: "${layoutSettings.marginTop}in ${layoutSettings.marginRight}in ${layoutSettings.marginBottom}in ${layoutSettings.marginLeft}in",
                  fontFamily: "Times New Roman, serif",
                  fontSize: "12pt",
                  lineHeight: "1.6",
                }}
              >
                <div
                  dangerouslySetInnerHTML={{ __html: processedContent }}
                  className="prose prose-sm max-w-none"
                />
              </div>
            </div>

            <div className="mt-4 text-center text-sm text-muted-foreground">
              <p>Preview at {Math.round(scale * 100)}% scale • ${layoutSettings.paperSize.toUpperCase()} ${layoutSettings.orientation} format</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

${processTemplateFunction}`;
}

function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
