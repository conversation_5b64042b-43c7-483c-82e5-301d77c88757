"use client";

import { useState } from "react";
import { TemplateUpload } from "./components/TemplateUpload";
import { DocumentEditor } from "./components/DocumentEditor";

export interface DocumentContent {
  html: string;
  text: string;
  originalFile: File;
  fileName: string;
}

export interface EditableContent {
  html: string;
  text: string;
}

export interface LayoutSettings {
  orientation: "portrait" | "landscape";
  paperSize: "a4" | "letter";
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

export default function TemplateUploadPage() {
  const [documentContent, setDocumentContent] =
    useState<DocumentContent | null>(null);
  const [editableContent, setEditableContent] =
    useState<EditableContent | null>(null);
  const [currentStep, setCurrentStep] = useState<"upload" | "edit">("upload");
  const [layoutSettings, setLayoutSettings] = useState<LayoutSettings>({
    orientation: "portrait",
    paperSize: "a4",
    marginTop: 1,
    marginBottom: 1,
    marginLeft: 1,
    marginRight: 1,
  });

  const handleDocumentUploaded = (content: DocumentContent) => {
    setDocumentContent(content);
    setEditableContent({
      html: content.html,
      text: content.text,
    });
    setCurrentStep("edit");
  };

  const handleContentEdited = (content: EditableContent) => {
    setEditableContent(content);
  };

  const handleLayoutChange = (settings: LayoutSettings) => {
    setLayoutSettings(settings);
  };

  const handleBackToUpload = () => {
    setCurrentStep("upload");
    setDocumentContent(null);
    setEditableContent(null);
  };

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      {currentStep === "upload" && (
        <TemplateUpload onDocumentUploaded={handleDocumentUploaded} />
      )}

      {currentStep === "edit" && documentContent && editableContent && (
        <DocumentEditor
          documentContent={documentContent}
          editableContent={editableContent}
          layoutSettings={layoutSettings}
          onContentChange={handleContentEdited}
          onLayoutChange={handleLayoutChange}
          onBack={handleBackToUpload}
        />
      )}
    </div>
  );
}
