"use client";

import { useState } from "react";
import { TemplateUpload } from "./components/TemplateUpload";
import { DocumentEditor } from "./components/DocumentEditor";
import { DocumentPreview } from "./components/DocumentPreview";

export interface DocumentContent {
  html: string;
  text: string;
  originalFile: File;
  fileName: string;
}

export interface EditableContent {
  html: string;
  text: string;
}

export default function TemplateUploadPage() {
  const [documentContent, setDocumentContent] = useState<DocumentContent | null>(null);
  const [editableContent, setEditableContent] = useState<EditableContent | null>(null);
  const [currentStep, setCurrentStep] = useState<'upload' | 'edit' | 'preview'>('upload');

  const handleDocumentUploaded = (content: DocumentContent) => {
    setDocumentContent(content);
    setEditableContent({
      html: content.html,
      text: content.text
    });
    setCurrentStep('edit');
  };

  const handleContentEdited = (content: EditableContent) => {
    setEditableContent(content);
  };

  const handlePreview = () => {
    setCurrentStep('preview');
  };

  const handleBackToEdit = () => {
    setCurrentStep('edit');
  };

  const handleBackToUpload = () => {
    setCurrentStep('upload');
    setDocumentContent(null);
    setEditableContent(null);
  };

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      {currentStep === 'upload' && (
        <TemplateUpload onDocumentUploaded={handleDocumentUploaded} />
      )}
      
      {currentStep === 'edit' && documentContent && editableContent && (
        <DocumentEditor
          documentContent={documentContent}
          editableContent={editableContent}
          onContentChange={handleContentEdited}
          onPreview={handlePreview}
          onBack={handleBackToUpload}
        />
      )}
      
      {currentStep === 'preview' && documentContent && editableContent && (
        <DocumentPreview
          documentContent={documentContent}
          editableContent={editableContent}
          onBack={handleBackToEdit}
          onEdit={handleBackToEdit}
        />
      )}
    </div>
  );
}
