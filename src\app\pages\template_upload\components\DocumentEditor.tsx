"use client";

import { useState, useRef, useEffect } from "react";
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Eye,
  ArrowLeft,
  Save,
  RotateCcw,
  Maximize,
  FileText,
  Indent,
  Outdent,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { DocumentContent, EditableContent, LayoutSettings } from "../page";

interface DocumentEditorProps {
  documentContent: DocumentContent;
  editableContent: EditableContent;
  layoutSettings: LayoutSettings;
  onContentChange: (content: EditableContent) => void;
  onLayoutChange: (settings: LayoutSettings) => void;
  onPreview: () => void;
  onBack: () => void;
}

export function DocumentEditor({
  documentContent,
  editableContent,
  layoutSettings,
  onContentChange,
  onLayoutChange,
  onPreview,
  onBack,
}: DocumentEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [selectedText, setSelectedText] = useState("");

  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = editableContent.html;
    }
  }, [editableContent.html]);

  const handleContentChange = () => {
    if (editorRef.current) {
      const html = editorRef.current.innerHTML;
      const text = editorRef.current.innerText;

      onContentChange({ html, text });
    }
  };

  const executeCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    handleContentChange();
    editorRef.current?.focus();
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection) {
      setSelectedText(selection.toString());
    }
  };

  const insertText = (text: string) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(document.createTextNode(text));
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
      handleContentChange();
    }
  };

  const changeFontSize = (size: string) => {
    executeCommand("fontSize", size);
  };

  const changeTextColor = (color: string) => {
    executeCommand("foreColor", color);
  };

  const handleIndent = () => {
    executeCommand("indent");
  };

  const handleOutdent = () => {
    executeCommand("outdent");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Tab") {
      e.preventDefault();
      if (e.shiftKey) {
        handleOutdent();
      } else {
        handleIndent();
      }
    }
  };

  const getPageWidth = () => {
    const { orientation, paperSize } = layoutSettings;

    if (paperSize === "a4") {
      return orientation === "portrait" ? "8.27in" : "11.69in";
    } else {
      // letter
      return orientation === "portrait" ? "8.5in" : "11in";
    }
  };

  const getPageHeight = () => {
    const { orientation, paperSize } = layoutSettings;

    if (paperSize === "a4") {
      return orientation === "portrait" ? "11.69in" : "8.27in";
    } else {
      // letter
      return orientation === "portrait" ? "11in" : "8.5in";
    }
  };

  const saveTemplate = () => {
    // Here you would typically save to a backend
    toast.success("Template saved successfully!");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Upload
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              Document Editor
            </h1>
            <p className="text-muted-foreground">
              Editing: {documentContent.fileName}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={saveTemplate}>
            <Save className="w-4 h-4 mr-2" />
            Save Template
          </Button>
          <Button onClick={onPreview}>
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Toolbar */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Formatting Tools</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Text Formatting */}
            <div>
              <h4 className="text-sm font-medium mb-2">Text Style</h4>
              <div className="flex flex-wrap gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("bold")}
                  className="p-2"
                >
                  <Bold className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("italic")}
                  className="p-2"
                >
                  <Italic className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("underline")}
                  className="p-2"
                >
                  <Underline className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <Separator />

            {/* Text Alignment */}
            <div>
              <h4 className="text-sm font-medium mb-2">Alignment</h4>
              <div className="flex flex-wrap gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("justifyLeft")}
                  className="p-2"
                >
                  <AlignLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("justifyCenter")}
                  className="p-2"
                >
                  <AlignCenter className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => executeCommand("justifyRight")}
                  className="p-2"
                >
                  <AlignRight className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <Separator />

            {/* Indentation */}
            <div>
              <h4 className="text-sm font-medium mb-2">Indentation</h4>
              <div className="flex flex-wrap gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleIndent}
                  className="p-2"
                  title="Indent (Tab)"
                >
                  <Indent className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOutdent}
                  className="p-2"
                  title="Outdent (Shift+Tab)"
                >
                  <Outdent className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Use Tab/Shift+Tab in editor
              </p>
            </div>

            <Separator />

            {/* Font Size */}
            <div>
              <h4 className="text-sm font-medium mb-2">Font Size</h4>
              <div className="grid grid-cols-3 gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeFontSize("1")}
                  className="text-xs"
                >
                  Small
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeFontSize("3")}
                  className="text-sm"
                >
                  Normal
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeFontSize("5")}
                  className="text-base"
                >
                  Large
                </Button>
              </div>
            </div>

            <Separator />

            {/* Text Color */}
            <div>
              <h4 className="text-sm font-medium mb-2">Text Color</h4>
              <div className="grid grid-cols-4 gap-1">
                <button
                  className="w-8 h-8 rounded border-2 border-border bg-black"
                  onClick={() => changeTextColor("#000000")}
                />
                <button
                  className="w-8 h-8 rounded border-2 border-border bg-red-600"
                  onClick={() => changeTextColor("#dc2626")}
                />
                <button
                  className="w-8 h-8 rounded border-2 border-border bg-blue-600"
                  onClick={() => changeTextColor("#2563eb")}
                />
                <button
                  className="w-8 h-8 rounded border-2 border-border bg-green-600"
                  onClick={() => changeTextColor("#16a34a")}
                />
              </div>
            </div>

            <Separator />

            {/* Layout Settings */}
            <div>
              <h4 className="text-sm font-medium mb-2">Page Layout</h4>
              <div className="space-y-3">
                {/* Orientation */}
                <div>
                  <label className="text-xs text-muted-foreground mb-1 block">
                    Orientation
                  </label>
                  <div className="grid grid-cols-2 gap-1">
                    <Button
                      variant={
                        layoutSettings.orientation === "portrait"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() =>
                        onLayoutChange({
                          ...layoutSettings,
                          orientation: "portrait",
                        })
                      }
                      className="text-xs"
                    >
                      <RotateCcw className="w-3 h-3 mr-1" />
                      Portrait
                    </Button>
                    <Button
                      variant={
                        layoutSettings.orientation === "landscape"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() =>
                        onLayoutChange({
                          ...layoutSettings,
                          orientation: "landscape",
                        })
                      }
                      className="text-xs"
                    >
                      <Maximize className="w-3 h-3 mr-1" />
                      Landscape
                    </Button>
                  </div>
                </div>

                {/* Paper Size */}
                <div>
                  <label className="text-xs text-muted-foreground mb-1 block">
                    Paper Size
                  </label>
                  <div className="grid grid-cols-2 gap-1">
                    <Button
                      variant={
                        layoutSettings.paperSize === "a4"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() =>
                        onLayoutChange({
                          ...layoutSettings,
                          paperSize: "a4",
                        })
                      }
                      className="text-xs"
                    >
                      <FileText className="w-3 h-3 mr-1" />
                      A4
                    </Button>
                    <Button
                      variant={
                        layoutSettings.paperSize === "letter"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() =>
                        onLayoutChange({
                          ...layoutSettings,
                          paperSize: "letter",
                        })
                      }
                      className="text-xs"
                    >
                      <FileText className="w-3 h-3 mr-1" />
                      Letter
                    </Button>
                  </div>
                </div>

                {/* Margins */}
                <div>
                  <label className="text-xs text-muted-foreground mb-2 block">
                    Margins (inches)
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Top
                      </label>
                      <input
                        type="number"
                        min="0.5"
                        max="2"
                        step="0.25"
                        value={layoutSettings.marginTop}
                        onChange={(e) =>
                          onLayoutChange({
                            ...layoutSettings,
                            marginTop: parseFloat(e.target.value),
                          })
                        }
                        className="w-full px-2 py-1 text-xs border border-border rounded"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Bottom
                      </label>
                      <input
                        type="number"
                        min="0.5"
                        max="2"
                        step="0.25"
                        value={layoutSettings.marginBottom}
                        onChange={(e) =>
                          onLayoutChange({
                            ...layoutSettings,
                            marginBottom: parseFloat(e.target.value),
                          })
                        }
                        className="w-full px-2 py-1 text-xs border border-border rounded"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Left
                      </label>
                      <input
                        type="number"
                        min="0.5"
                        max="2"
                        step="0.25"
                        value={layoutSettings.marginLeft}
                        onChange={(e) =>
                          onLayoutChange({
                            ...layoutSettings,
                            marginLeft: parseFloat(e.target.value),
                          })
                        }
                        className="w-full px-2 py-1 text-xs border border-border rounded"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground">
                        Right
                      </label>
                      <input
                        type="number"
                        min="0.5"
                        max="2"
                        step="0.25"
                        value={layoutSettings.marginRight}
                        onChange={(e) =>
                          onLayoutChange({
                            ...layoutSettings,
                            marginRight: parseFloat(e.target.value),
                          })
                        }
                        className="w-full px-2 py-1 text-xs border border-border rounded"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Quick Actions */}
            <div>
              <h4 className="text-sm font-medium mb-2">Quick Insert</h4>
              <div className="space-y-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => insertText("[Name]")}
                >
                  Insert [Name]
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => insertText("[Date]")}
                >
                  Insert [Date]
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => insertText("[Position]")}
                >
                  Insert [Position]
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Editor */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-lg">Document Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              ref={editorRef}
              contentEditable
              className="border border-border rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-primary"
              style={{
                fontFamily: "Times New Roman, serif",
                fontSize: "14px",
                lineHeight: "1.6",
                width: getPageWidth(),
                minHeight: getPageHeight(),
                padding: `${layoutSettings.marginTop}in ${layoutSettings.marginRight}in ${layoutSettings.marginBottom}in ${layoutSettings.marginLeft}in`,
                margin: "0 auto",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
              onInput={handleContentChange}
              onMouseUp={handleTextSelection}
              onKeyUp={handleTextSelection}
              onKeyDown={handleKeyDown}
              suppressContentEditableWarning={true}
            />

            {selectedText && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Selected text:{" "}
                  <span className="font-medium">
                    &ldquo;{selectedText}&rdquo;
                  </span>
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
