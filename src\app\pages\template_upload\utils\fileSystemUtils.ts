import { TemplateData, FormField } from "./templateGenerator";

export interface TemplateFiles {
  pageComponent: string;
  formComponent: string;
  previewComponent: string;
  formFields: FormField[];
}

export async function createTemplateFolder(
  templateData: TemplateData,
  templateFiles: TemplateFiles
): Promise<void> {
  try {
    // In a real implementation, this would make API calls to create actual files
    // For now, we'll simulate the process and provide the file structure
    
    const folderPath = `src/app/pages/${templateData.name}`;
    const componentsPath = `${folderPath}/components`;
    
    console.log(`Creating template folder structure at: ${folderPath}`);
    
    // Simulate creating the folder structure
    const fileStructure = {
      [`${folderPath}/page.tsx`]: templateFiles.pageComponent,
      [`${componentsPath}/${capitalizeFirst(templateData.name)}Form.tsx`]: templateFiles.formComponent,
      [`${componentsPath}/${capitalizeFirst(templateData.name)}Preview.tsx`]: templateFiles.previewComponent,
      [`${componentsPath}/index.ts`]: generateIndexFile(templateData.name),
    };
    
    // Log the file structure that would be created
    console.log("Files to be created:");
    Object.entries(fileStructure).forEach(([path, content]) => {
      console.log(`\n=== ${path} ===`);
      console.log(content);
    });
    
    // In a real implementation, you would:
    // 1. Make API calls to your backend to create these files
    // 2. Or use a file system API if running in a Node.js environment
    // 3. Update your routing system to recognize the new template
    
    // For demonstration, we'll show what the API call might look like:
    /*
    const response = await fetch('/api/templates/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        templateData,
        fileStructure,
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create template files');
    }
    */
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(`Template "${templateData.displayName}" created successfully!`);
    
  } catch (error) {
    console.error("Error creating template folder:", error);
    throw error;
  }
}

function generateIndexFile(templateName: string): string {
  const componentName = capitalizeFirst(templateName);
  return `export { ${componentName}Form } from "./${componentName}Form";
export { ${componentName}Preview } from "./${componentName}Preview";
`;
}

function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// Utility function to validate template name
export function validateTemplateName(name: string): boolean {
  // Check if name is valid for folder/file creation
  const validNameRegex = /^[a-z][a-z0-9_]*$/;
  return validNameRegex.test(name);
}

// Utility function to check if template already exists
export async function checkTemplateExists(templateName: string): Promise<boolean> {
  try {
    // In a real implementation, this would check if the folder/files already exist
    // For now, we'll simulate this check

    // You could make an API call like:
    /*
    const response = await fetch(`/api/templates/check/${templateName}`);
    return response.ok;
    */

    // For demonstration, we'll assume templates don't exist
    console.log(`Checking if template "${templateName}" exists...`);
    return false;
  } catch (error) {
    console.error("Error checking template existence:", error);
    return false;
  }
}

// Utility function to get list of existing templates
export async function getExistingTemplates(): Promise<string[]> {
  try {
    // In a real implementation, this would fetch the list of existing templates
    // For now, we'll return a mock list
    
    // You could make an API call like:
    /*
    const response = await fetch('/api/templates/list');
    const templates = await response.json();
    return templates.map(t => t.name);
    */
    
    // For demonstration, return some mock templates
    return ['good_moral', 'business_permit', 'certificate_of_employment'];
  } catch (error) {
    console.error("Error fetching existing templates:", error);
    return [];
  }
}
