"use client";

import { useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Edit, FileText } from "lucide-react";
import { toast } from "sonner";
import { DocumentContent, EditableContent, LayoutSettings } from "../page";
import { generatePDF } from "../utils/pdfGenerator";

interface DocumentPreviewProps {
  documentContent: DocumentContent;
  editableContent: EditableContent;
  layoutSettings: LayoutSettings;
  onBack: () => void;
  onEdit: () => void;
}

export function DocumentPreview({
  documentContent,
  editableContent,
  layoutSettings,
  onBack,
  onEdit,
}: DocumentPreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(0.8);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const handleDownloadPDF = async () => {
    if (!previewRef.current) return;

    setIsGeneratingPDF(true);
    try {
      await generatePDF(previewRef.current, {
        fileName: documentContent.fileName.replace(".docx", ".pdf"),
        orientation: layoutSettings.orientation,
        paperSize: layoutSettings.paperSize,
      });
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleDownloadDOCX = () => {
    // Create a simple HTML to DOCX conversion
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Document</title>
          <style>
            body { 
              font-family: 'Times New Roman', serif; 
              font-size: 12pt; 
              line-height: 1.6; 
              margin: 1in; 
            }
          </style>
        </head>
        <body>
          ${editableContent.html}
        </body>
      </html>
    `;

    const blob = new Blob([htmlContent], {
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = documentContent.fileName.replace(".docx", "_edited.docx");
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("DOCX file downloaded!");
  };

  const getPageWidth = () => {
    const { orientation, paperSize } = layoutSettings;

    if (paperSize === "a4") {
      return orientation === "portrait" ? "8.27in" : "11.69in";
    } else {
      // letter
      return orientation === "portrait" ? "8.5in" : "11in";
    }
  };

  const getPageHeight = () => {
    const { orientation, paperSize } = layoutSettings;

    if (paperSize === "a4") {
      return orientation === "portrait" ? "11.69in" : "8.27in";
    } else {
      // letter
      return orientation === "portrait" ? "11in" : "8.5in";
    }
  };

  const getPageDimensions = () => {
    const { orientation, paperSize } = layoutSettings;

    if (paperSize === "a4") {
      return orientation === "portrait" ? '8.27" × 11.69"' : '11.69" × 8.27"';
    } else {
      // letter
      return orientation === "portrait" ? '8.5" × 11"' : '11" × 8.5"';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Editor
          </Button>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              Document Preview
            </h1>
            <p className="text-muted-foreground">
              Preview: {documentContent.fileName}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Document
          </Button>
          <Button variant="outline" onClick={handleDownloadDOCX}>
            <FileText className="w-4 h-4 mr-2" />
            Download DOCX
          </Button>
          <Button onClick={handleDownloadPDF} disabled={isGeneratingPDF}>
            <Download className="w-4 h-4 mr-2" />
            {isGeneratingPDF ? "Generating..." : "Download PDF"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Controls */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Preview Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Zoom Level</h4>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(0.6)}
                >
                  60%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(0.8)}
                >
                  80%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => setScale(1.0)}
                >
                  100%
                </Button>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Document Info</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>
                  <span className="font-medium">Original:</span>{" "}
                  {documentContent.fileName}
                </p>
                <p>
                  <span className="font-medium">Size:</span>{" "}
                  {(documentContent.originalFile.size / 1024).toFixed(1)} KB
                </p>
                <p>
                  <span className="font-medium">Type:</span> DOCX Document
                </p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Layout Settings</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>
                  <span className="font-medium">Orientation:</span>{" "}
                  {layoutSettings.orientation === "portrait"
                    ? "Portrait"
                    : "Landscape"}
                </p>
                <p>
                  <span className="font-medium">Paper Size:</span>{" "}
                  {layoutSettings.paperSize.toUpperCase()}
                </p>
                <p>
                  <span className="font-medium">Dimensions:</span>{" "}
                  {getPageDimensions()}
                </p>
                <p>
                  <span className="font-medium">Margins:</span> T:
                  {layoutSettings.marginTop}&rdquo; R:
                  {layoutSettings.marginRight}&rdquo; B:
                  {layoutSettings.marginBottom}&rdquo; L:
                  {layoutSettings.marginLeft}&rdquo;
                </p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Quick Actions</h4>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={onEdit}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Continue Editing
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={handleDownloadPDF}
                  disabled={isGeneratingPDF}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export as PDF
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-lg">Document Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-8 rounded-lg overflow-auto max-h-[800px]">
              <div
                ref={previewRef}
                className="bg-white shadow-lg mx-auto"
                style={{
                  transform: `scale(${scale})`,
                  transformOrigin: "top center",
                  width: getPageWidth(),
                  minHeight: getPageHeight(),
                  padding: `${layoutSettings.marginTop}in ${layoutSettings.marginRight}in ${layoutSettings.marginBottom}in ${layoutSettings.marginLeft}in`,
                  fontFamily: "Times New Roman, serif",
                  fontSize: "12pt",
                  lineHeight: "1.6",
                }}
              >
                <div
                  dangerouslySetInnerHTML={{ __html: editableContent.html }}
                  className="prose prose-sm max-w-none"
                />
              </div>
            </div>

            <div className="mt-4 text-center text-sm text-muted-foreground">
              <p>
                Preview at {Math.round(scale * 100)}% scale •{" "}
                {layoutSettings.paperSize.toUpperCase()}{" "}
                {layoutSettings.orientation} format ({getPageDimensions()})
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
